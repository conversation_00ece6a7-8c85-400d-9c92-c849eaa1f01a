/* Role Custom Para Çek<PERSON> - Modern Tasarım */

.role-custom-withdraw-page {
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* Modern Header */
.role-custom-withdraw-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    margin: 0 0 0 -20px;
    margin-top: -10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.withdraw-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    max-width: 1200px;
    margin: 0 auto;
    gap: 30px;
}

.withdraw-title-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    flex: 1;
}



.withdraw-title-section h1 {
    margin: 0;
    font-size: 32px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.header-subtitle {
    margin: 8px 0 0 0;
    font-size: 16px;
    opacity: 0.9;
    color: white;
    line-height: 1.4;
}



/* Hızlı Bakiye Özeti */
.quick-balance-summary {
    display: flex;
    gap: 20px;
    flex-shrink: 0;
}

.quick-balance-item {
    text-align: center;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 120px;
    transition: all 0.3s ease;
}

.quick-balance-item:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.quick-balance-label {
    display: block;
    font-size: 11px;
    opacity: 0.9;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.quick-balance-amount {
    display: block;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.2;
}

/* Container */
.role-custom-withdraw-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 60px 20px;
    margin-top: -20px;
    position: relative;
    z-index: 1;
}

/* Bakiye Kartları Grid */
.balance-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.balance-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e1e1;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
}

.balance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.balance-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.balance-card.total-earnings .balance-card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.balance-card.withdrawn .balance-card-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.balance-card.available .balance-card-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.balance-card.pending .balance-card-icon {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #8b4513;
}

.balance-card-content {
    flex: 1;
}

.balance-card-label {
    display: block;
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.balance-card-amount {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #495057;
    margin-bottom: 2px;
}

.balance-card-desc {
    display: block;
    font-size: 12px;
    color: #6c757d;
}

/* Ana Grid */
.withdraw-main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

@media (max-width: 768px) {
    .withdraw-main-grid {
        grid-template-columns: 1fr;
    }
}

/* Withdraw Sections */
.withdraw-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    background: #f8f9fa;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-header .dashicons {
    font-size: 20px;
    color: #6c757d;
}

.section-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #495057;
}

/* Hesap Detayları */
.account-details-card {
    padding: 24px;
}

.account-info-modern {
    margin-bottom: 24px;
}

.account-field {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 0;
    border-bottom: 1px solid #f1f3f4;
}

.account-field:last-child {
    border-bottom: none;
}

.field-icon {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 16px;
}

.field-content {
    flex: 1;
}

.field-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.field-value {
    display: block;
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

.iban-display {
    font-family: monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
}

.no-account-modern {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-account-modern .dashicons {
    font-size: 48px;
    opacity: 0.5;
    margin-bottom: 16px;
}

.no-account-modern h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-weight: 600;
}

.no-account-modern p {
    margin: 0;
    font-size: 14px;
}

/* Form Stilleri */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #1d2327;
}

.form-group input,
.form-group textarea {
    width: 100%;
    max-width: 300px;
}

.form-group .description {
    margin-top: 5px;
    color: #646970;
    font-size: 13px;
}

/* Modern Butonlar */
.btn-modern {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: none;
}

.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-modern.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-modern.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-modern.btn-large {
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 600;
}

.btn-modern:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Para Çekme Formu */
.withdraw-form-card {
    padding: 24px;
}

.modern-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.field-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.field-label .dashicons {
    font-size: 16px;
    color: #6c757d;
}

.amount-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.amount-input {
    flex: 1;
    padding: 12px 16px;
    padding-right: 40px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    transition: border-color 0.2s ease;
}

.amount-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.currency-symbol {
    position: absolute;
    right: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
}

.note-textarea {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
    transition: border-color 0.2s ease;
}

.note-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.field-help {
    margin-top: 4px;
}

.help-text {
    font-size: 12px;
    color: #6c757d;
}

.form-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    color: #856404;
    font-size: 14px;
    margin-top: 16px;
}

/* Durum Mesajları */
.insufficient-balance,
.no-balance-modern {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.insufficient-balance .dashicons,
.no-balance-modern .dashicons {
    font-size: 48px;
    opacity: 0.5;
    margin-bottom: 16px;
    color: #ffc107;
}

.insufficient-balance h4,
.no-balance-modern h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-weight: 600;
}

.insufficient-balance p,
.no-balance-modern p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Modal Stilleri */
.role-custom-modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.role-custom-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border-radius: 4px;
    width: 90%;
    max-width: 500px;
    position: relative;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.role-custom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.role-custom-modal-header h3 {
    margin: 0;
    color: #1d2327;
}

.role-custom-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.role-custom-modal-close:hover {
    color: #d63638;
}

.role-custom-modal-body {
    margin-bottom: 20px;
}

.role-custom-modal-footer {
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.role-custom-modal-footer .button {
    margin-left: 10px;
}

/* Para Çekme Geçmişi */
.withdraw-history {
    margin-top: 30px;
}

.withdraw-history table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.withdraw-history th,
.withdraw-history td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.withdraw-history th {
    background-color: #f9f9f9;
    font-weight: 600;
}

.withdraw-status {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.withdraw-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.withdraw-status.approved {
    background-color: #d1edff;
    color: #0c5460;
}

.withdraw-status.rejected {
    background-color: #f8d7da;
    color: #721c24;
}

/* Loading Durumu */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Para Çekme Geçmişi - Modern */
.withdraw-history-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.history-card {
    padding: 0;
}

.empty-history {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-history .dashicons {
    font-size: 64px;
    opacity: 0.3;
    margin-bottom: 20px;
}

.empty-history h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-weight: 600;
}

.empty-history p {
    margin: 0;
    font-size: 14px;
}

.history-table-wrapper {
    overflow-x: auto;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.modern-table thead th {
    background: #f8f9fa;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-table thead th .dashicons {
    margin-right: 6px;
    opacity: 0.7;
}

.history-row {
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.history-row:hover {
    background-color: #f8f9fa;
}

.history-row td {
    padding: 16px;
    vertical-align: middle;
}

.amount-cell {
    display: flex;
    align-items: baseline;
    gap: 4px;
}

.amount-value {
    font-size: 16px;
    font-weight: 600;
    color: #28a745;
}

.currency {
    font-size: 14px;
    color: #6c757d;
}

.date-cell {
    display: flex;
    flex-direction: column;
}

.date-value {
    font-weight: 500;
    color: #495057;
}

.time-value {
    font-size: 12px;
    color: #6c757d;
}

.note-text {
    color: #6c757d;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .balance-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .quick-balance-summary {
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .role-custom-withdraw-header {
        padding: 20px;
        margin-left: -10px;
    }

    .withdraw-header-content {
        flex-direction: column;
        align-items: center;
        gap: 20px;
        text-align: center;
    }

    .withdraw-title-section {
        align-items: center;
        text-align: center;
    }

    .quick-balance-summary {
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .quick-balance-item {
        min-width: 100px;
        padding: 12px 16px;
    }

    .quick-balance-amount {
        font-size: 18px;
    }

    .role-custom-withdraw-container {
        padding: 20px 10px;
    }

    .balance-cards-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .balance-card {
        padding: 20px;
    }

    .withdraw-main-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .section-header {
        padding: 16px 20px;
    }

    .account-details-card,
    .withdraw-form-card {
        padding: 20px;
    }

    .role-custom-modal-content {
        margin: 10% auto;
        width: 95%;
        padding: 15px;
    }

    .modern-table {
        font-size: 13px;
    }

    .modern-table th,
    .modern-table td {
        padding: 12px 8px;
    }
}

/* Başarı ve Hata Mesajları */
.role-custom-notice {
    padding: 12px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.role-custom-notice.success {
    background-color: #d1edff;
    border-left-color: #00a32a;
    color: #00a32a;
}

.role-custom-notice.error {
    background-color: #f8d7da;
    border-left-color: #d63638;
    color: #d63638;
}

.role-custom-notice.warning {
    background-color: #fff3cd;
    border-left-color: #dba617;
    color: #856404;
}

/* WordPress Footer Gizle */
body.role-custom-withdraw-page #wpfooter,
.role-custom-withdraw-page #wpfooter,
#wpfooter {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    overflow: hidden !important;
}
