/**
 * Role Custom Reports JavaScript
 */

(function($) {
    'use strict';

    // Global değişkenler
    let salesChart = null;
    let revenueChart = null;
    let productsChart = null;

    // Say<PERSON> yüklendiğinde başlat
    $(document).ready(function() {
        initializeReports();
        bindEvents();
        loadInitialData();
    });

    /**
     * Reports sayfasını başlat
     */
    function initializeReports() {
        // Özet kartlarını güncelle
        updateSummaryCards();
        
        // Chart.js global ayarları
        Chart.defaults.font.family = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        Chart.defaults.color = '#666';
        Chart.defaults.borderColor = '#e1e1e1';
    }

    /**
     * Event listener'ları bağla
     */
    function bindEvents() {
        // Satış periyodu değişikliği
        $('#sales-period').on('change', function() {
            const value = $(this).val();
            if (value === 'custom') {
                $('#sales-custom-dates').show();
            } else {
                $('#sales-custom-dates').hide();
                loadSalesData(value);
            }
        });

        // Gelir periyodu değişikliği
        $('#revenue-period').on('change', function() {
            const value = $(this).val();
            if (value === 'custom') {
                $('#revenue-custom-dates').show();
            } else {
                $('#revenue-custom-dates').hide();
                loadRevenueData(value);
            }
        });

        // Kazanç periyodu değişikliği
        $('#earnings-period').on('change', function() {
            const value = $(this).val();
            if (value === 'custom') {
                $('#earnings-custom-dates').show();
            } else {
                $('#earnings-custom-dates').hide();
                loadEarningsData(value);
            }
        });

        // Özel tarih aralığı uygula butonları
        $('#sales-apply-dates').on('click', function() {
            const startDate = $('#sales-start-date').val();
            const endDate = $('#sales-end-date').val();

            if (!startDate || !endDate) {
                alert('Lütfen başlangıç ve bitiş tarihlerini seçin.');
                return;
            }

            if (startDate > endDate) {
                alert('Başlangıç tarihi bitiş tarihinden sonra olamaz.');
                return;
            }

            loadSalesDataByRange(startDate, endDate);
        });

        $('#revenue-apply-dates').on('click', function() {
            const startDate = $('#revenue-start-date').val();
            const endDate = $('#revenue-end-date').val();

            if (!startDate || !endDate) {
                alert('Lütfen başlangıç ve bitiş tarihlerini seçin.');
                return;
            }

            if (startDate > endDate) {
                alert('Başlangıç tarihi bitiş tarihinden sonra olamaz.');
                return;
            }

            loadRevenueDataByRange(startDate, endDate);
        });

        $('#earnings-apply-dates').on('click', function() {
            const startDate = $('#earnings-start-date').val();
            const endDate = $('#earnings-end-date').val();

            if (!startDate || !endDate) {
                alert('Lütfen başlangıç ve bitiş tarihlerini seçin.');
                return;
            }

            if (startDate > endDate) {
                alert('Başlangıç tarihi bitiş tarihinden sonra olamaz.');
                return;
            }

            loadEarningsDataByRange(startDate, endDate);
        });
    }

    /**
     * İlk verileri yükle
     */
    function loadInitialData() {
        // Hangi sayfada olduğumuzu kontrol et
        const currentUrl = window.location.href;

        if (currentUrl.includes('role-custom-reports-earnings')) {
            // Kazanç sayfası
            loadEarningsData(30).catch((error) => {
                console.error('Kazanç veri yükleme hatası:', error);
                showError(roleCustomReports.strings.error);
            });
        } else if (currentUrl.includes('role-custom-reports-products')) {
            // Ürünler sayfası
            loadProductPerformance().catch((error) => {
                console.error('Ürün veri yükleme hatası:', error);
                showError(roleCustomReports.strings.error);
            });
        } else if (currentUrl.includes('role-custom-reports-courses')) {
            // Kurslar sayfası
            loadCoursesData().catch((error) => {
                console.error('Kurs veri yükleme hatası:', error);
                showError(roleCustomReports.strings.error);
            });
        } else {
            // Genel Bakış sayfası (varsayılan)
            Promise.all([
                loadSalesData(30),
                loadRevenueData(30),
                loadProductPerformance(),
                loadCoursesData()
            ]).catch((error) => {
                console.error('Veri yükleme hatası:', error);
                showError(roleCustomReports.strings.error);
            });
        }
    }

    /**
     * Özet kartlarını güncelle
     */
    function updateSummaryCards() {
        if (typeof roleCustomStats !== 'undefined') {
            $('#total-orders').text(roleCustomStats.total_orders.toLocaleString());
            $('#total-revenue').text(roleCustomStats.total_revenue.toLocaleString('tr-TR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }) + ' TL');
            $('#total-products').text(roleCustomStats.total_products.toLocaleString());
            $('#avg-rating').text(roleCustomStats.avg_rating > 0 ? roleCustomStats.avg_rating + '/5' : '-');
        }

        // Kurs istatistiklerini güncelle
        if (typeof roleCustomCourseStats !== 'undefined') {
            $('#total-courses').text(roleCustomCourseStats.total_courses.toLocaleString());
            $('#published-courses').text(roleCustomCourseStats.published_courses.toLocaleString());
            $('#total-students').text(roleCustomCourseStats.total_students.toLocaleString());
            $('#total-lessons').text(roleCustomCourseStats.total_lessons.toLocaleString());
            $('#total-quizzes').text(roleCustomCourseStats.total_quizzes.toLocaleString());
            $('#avg-course-rating').text(roleCustomCourseStats.avg_course_rating > 0 ? roleCustomCourseStats.avg_course_rating + '/5' : '-');
        }
    }

    /**
     * Satış verilerini yükle
     */
    function loadSalesData(days = 30) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: roleCustomReports.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'role_custom_get_sales_data',
                    days: days,
                    nonce: roleCustomReports.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderSalesChart(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('AJAX hatası');
                }
            });
        });
    }

    /**
     * Satış verilerini tarih aralığına göre yükle
     */
    function loadSalesDataByRange(startDate, endDate) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: roleCustomReports.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'role_custom_get_sales_data',
                    start_date: startDate,
                    end_date: endDate,
                    nonce: roleCustomReports.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderSalesChart(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('AJAX hatası');
                }
            });
        });
    }

    /**
     * Gelir verilerini yükle
     */
    function loadRevenueData(days = 30) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: roleCustomReports.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'role_custom_get_revenue_data',
                    days: days,
                    nonce: roleCustomReports.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderRevenueChart(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('AJAX hatası');
                }
            });
        });
    }

    /**
     * Gelir verilerini tarih aralığına göre yükle
     */
    function loadRevenueDataByRange(startDate, endDate) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: roleCustomReports.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'role_custom_get_revenue_data',
                    start_date: startDate,
                    end_date: endDate,
                    nonce: roleCustomReports.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderRevenueChart(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('AJAX hatası');
                }
            });
        });
    }

    /**
     * Kazanç verilerini yükle
     */
    function loadEarningsData(days = 30) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: roleCustomReports.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'role_custom_get_revenue_data',
                    days: days,
                    nonce: roleCustomReports.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderEarningsChart(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('AJAX hatası');
                }
            });
        });
    }

    /**
     * Kazanç verilerini tarih aralığına göre yükle
     */
    function loadEarningsDataByRange(startDate, endDate) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: roleCustomReports.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'role_custom_get_revenue_data',
                    start_date: startDate,
                    end_date: endDate,
                    nonce: roleCustomReports.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderEarningsChart(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('AJAX hatası');
                }
            });
        });
    }

    /**
     * Ürün performans verilerini yükle
     */
    function loadProductPerformance() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: roleCustomReports.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'role_custom_get_product_performance',
                    limit: 10,
                    nonce: roleCustomReports.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderProductsChart(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('AJAX hatası');
                }
            });
        });
    }

    /**
     * Satış grafiğini render et
     */
    function renderSalesChart(data) {
        const ctx = document.getElementById('sales-chart');
        if (!ctx) return;

        // Mevcut grafiği yok et
        if (salesChart) {
            salesChart.destroy();
        }

        const labels = data.map(item => formatDate(item.date));
        const orders = data.map(item => item.orders);

        salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: roleCustomReports.strings.orders,
                    data: orders,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7
                }]
            },
            options: getChartOptions('Sipariş Sayısı')
        });
    }

    /**
     * Gelir grafiğini render et
     */
    function renderRevenueChart(data) {
        const ctx = document.getElementById('revenue-chart');
        if (!ctx) return;

        // Mevcut grafiği yok et
        if (revenueChart) {
            revenueChart.destroy();
        }

        const labels = data.map(item => formatDate(item.date));
        const revenue = data.map(item => item.revenue);

        const revenueOptions = getChartOptions('Gelir (TL)');

        // Gelir için özel tooltip
        revenueOptions.plugins.tooltip.callbacks = {
            label: function(context) {
                const value = context.parsed.y;
                return roleCustomReports.strings.revenue + ': ' +
                       value.toLocaleString('tr-TR', {
                           minimumFractionDigits: 2,
                           maximumFractionDigits: 2
                       }) + ' TL';
            }
        };

        revenueChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: roleCustomReports.strings.revenue,
                    data: revenue,
                    backgroundColor: 'rgba(245, 87, 108, 0.8)',
                    borderColor: '#f5576c',
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                }]
            },
            options: revenueOptions
        });
    }

    /**
     * Kazanç grafiğini render et
     */
    function renderEarningsChart(data) {
        const ctx = document.getElementById('earnings-chart');
        if (!ctx) return;

        // Mevcut grafiği yok et
        if (window.earningsChart) {
            window.earningsChart.destroy();
        }

        const labels = data.map(item => formatDate(item.date));
        const revenue = data.map(item => item.revenue);

        const earningsOptions = getChartOptions('Kazanç (TL)');

        // Kazanç için özel tooltip
        earningsOptions.plugins.tooltip.callbacks = {
            label: function(context) {
                const value = context.parsed.y;
                return 'Kazanç: ' +
                       value.toLocaleString('tr-TR', {
                           minimumFractionDigits: 2,
                           maximumFractionDigits: 2
                       }) + ' TL';
            }
        };

        window.earningsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Kazanç',
                    data: revenue,
                    backgroundColor: 'rgba(52, 168, 83, 0.1)',
                    borderColor: '#34a853',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#34a853',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4
                }]
            },
            options: earningsOptions
        });
    }

    /**
     * Ürün performans grafiğini render et
     */
    function renderProductsChart(data) {
        const ctx = document.getElementById('products-chart');
        if (!ctx) return;

        // Mevcut grafiği yok et
        if (productsChart) {
            productsChart.destroy();
        }

        if (!data || data.length === 0) {
            showEmptyChart(ctx, 'Henüz satış verisi yok');
            return;
        }

        const labels = data.map(item => item.product_name.length > 20 ? 
            item.product_name.substring(0, 20) + '...' : item.product_name);
        const sales = data.map(item => parseInt(item.total_sold));
        
        // Renk paleti
        const colors = [
            '#667eea', '#f5576c', '#4facfe', '#43e97b', '#fa709a',
            '#fee140', '#a8edea', '#ffecd2', '#d299c2', '#ffeaa7'
        ];

        productsChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: sales,
                    backgroundColor: colors.slice(0, data.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                return label + ': ' + value + ' adet';
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Chart seçeneklerini al
     */
    function getChartOptions(yAxisLabel) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxTicksLimit: 10
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    title: {
                        display: true,
                        text: yAxisLabel,
                        font: {
                            weight: 'bold'
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        };
    }

    /**
     * Tarihi formatla
     */
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('tr-TR', {
            day: '2-digit',
            month: '2-digit'
        });
    }

    /**
     * Boş grafik göster
     */
    function showEmptyChart(ctx, message) {
        const parent = ctx.parentElement;
        parent.innerHTML = '<div class="role-custom-empty-state">' +
            '<span class="dashicons dashicons-chart-bar"></span>' +
            '<h3>Veri Bulunamadı</h3>' +
            '<p>' + message + '</p>' +
            '</div>';
    }



    /**
     * Kurs verilerini yükle
     */
    function loadCoursesData() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: roleCustomReports.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'role_custom_get_courses_data',
                    nonce: roleCustomReports.nonce
                },
                success: function(response) {
                    if (response.success) {
                        renderCoursesChart(response.data);
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('AJAX hatası');
                }
            });
        });
    }

    /**
     * Kurs performans grafiğini render et
     */
    function renderCoursesChart(data) {
        const ctx = document.getElementById('courses-chart');
        if (!ctx) return;

        // Mevcut grafiği yok et
        if (typeof coursesChart !== 'undefined') {
            coursesChart.destroy();
        }

        if (!data || data.length === 0) {
            showEmptyChart(ctx, 'Henüz kurs verisi yok');
            return;
        }

        const labels = data.map(item => item.course_name.length > 20 ?
            item.course_name.substring(0, 20) + '...' : item.course_name);
        const students = data.map(item => parseInt(item.student_count));

        // Renk paleti
        const colors = [
            '#667eea', '#f5576c', '#4facfe', '#43e97b', '#fa709a',
            '#fee140', '#a8edea', '#ffecd2', '#d299c2', '#ffeaa7'
        ];

        window.coursesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Öğrenci Sayısı',
                    data: students,
                    backgroundColor: colors.slice(0, data.length),
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 6,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                const courseData = data[context.dataIndex];
                                return [
                                    'Öğrenci: ' + context.parsed.y + ' kişi',
                                    'Puan: ' + (courseData.rating > 0 ? courseData.rating + '/5' : 'Henüz puanlanmamış')
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            maxTicksLimit: 10
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        title: {
                            display: true,
                            text: 'Öğrenci Sayısı',
                            font: {
                                weight: 'bold'
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    /**
     * Hata mesajı göster
     */
    function showError(message) {
        const errorHtml = '<div class="role-custom-error"><p>' + message + '</p></div>';
        $('.role-custom-reports h1').after(errorHtml);

        // 5 saniye sonra kaldır
        setTimeout(function() {
            $('.role-custom-error').fadeOut();
        }, 5000);
    }

})(jQuery);
